# 机器人团队代码仓库

本文档提供了所有Gitea仓库的HTTP和SSH访问链接，按照项目类别进行分类。

## 机械控制与电子

1. **机械臂主控**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-arm-v1.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-arm-v1.git

2. **机械臂夹爪控制板**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-claw-v1.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-claw-v1.git

3. **步进电机驱动**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Ctrl-Step-Motor.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Ctrl-Step-Motor.git

4. **充电站充电板**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-24in1Charge.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-24in1Charge.git

5. **充电站1.0控制台代码**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-CR-Ctrl.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-CR-Ctrl.git

6. **随控(Mobile ALOHA)**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-MobileALOHA.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-MobileALOHA.git

7. **机械臂遥控器代码**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-Ctrl.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-Ctrl.git

8. **自动化B03电池产线 - 模块 镭雕+除尘+电池检测**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-B03Bat-Modules.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-B03Bat-Modules.git

9. **确认充电底座**
    - HTTP链接: http://zlxrobot.cn:20818/roboteam/B03-LedChargeBase.git
    - SSH链接: ssh://<EMAIL>:50023/roboteam/B03-LedChargeBase.git

## 校准与测试

1. **F09红外避障校准源码**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-F09IR-Calib.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-F09IR-Calib.git

2. **无人机IMU校准设备**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-IMU-Calib.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-IMU-Calib.git

3. **步进电机驱动板测试模块**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-StepPCBA-Test.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-StepPCBA-Test.git

4. **B03电池产测工具**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-B03Bat-TestTool.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-B03Bat-TestTool.git

## 软件代码

1. **中控软件 (用于电池生产线)**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-UI-BatteryPL.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-UI-BatteryPL.git

2. **轨迹规划上位机**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-UI-PathCtrl.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-UI-PathCtrl.git

3. **K230视觉模块代码库**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-Vision-K230.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-Vision-K230.git

4. **机械灵巧手视觉(猜拳)**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-Vision-DexHand.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-Vision-DexHand.git

5. **AI视觉大四轴**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-Vision-QuadAxis.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-Vision-QuadAxis.git

6. **充电站2.0控制台代码**
   - HTTP链接: http://zlxrobot.cn:20818/roboteam/Robot-CR2-UICtrl.git
   - SSH链接: ssh://<EMAIL>:50023/roboteam/Robot-CR2-UICtrl.git

使用说明

HTTP克隆（使用用户名密码）
git clone http://用户名:密码@zlxrobot.cn:20818/roboteam/仓库名.git

例如：
git clone http://abc:<EMAIL>:20818/roboteam/Robot-arm-v1.git

SSH克隆（使用SSH密钥）
# 确保已添加SSH密钥到Gitea账户
git clone ssh://<EMAIL>:50023/roboteam/仓库名.git

例如：
git clone ssh://<EMAIL>:50023/roboteam/Robot-arm-v1.git

使用SSH配置文件简化命令
在~/.ssh/config文件中添加以下配置：
Host gitea
    HostName zlxrobot.cn
    User sc-gitea
    Port 50023
    IdentityFile ~/.ssh/你的私钥文件

然后可以使用简化命令：
git clone gitea:roboteam/仓库名.git

注意事项

1. 使用HTTP链接时，需要输入Gitea账户的用户名和密码
2. 使用SSH链接时，需要先将SSH公钥添加到Gitea账户中
3. 所有用户通过SSH访问时都使用相同的用户名（sc-gitea），但Gitea会通过SSH密钥识别用户身份
4. 请参考《Gitea_SSH访问指南.md》文档了解如何生成和配置SSH密钥
